<template>
  <div class="volume-control-panel">
    <div class="panel-header">
      <div class="header-title">
        <!-- <el-icon><Headset /></el-icon> -->
        <span>音量（开发中.）</span>
      </div>
    </div>
    
    <div class="panel-content">
      <!-- 当前分镜视频音量 -->
      <div class="volume-section">
        <div class="volume-label" :class="{ 'disabled': !currentShot || currentShot.type !== 'video' }">
          <el-icon><VideoCamera /></el-icon>
          <span>视频</span>
        </div>
        <div class="volume-control">
          <el-slider
            v-model="videoVolume"
            :min="0"
            :max="150"
            :step="1"
            :show-tooltip="true"
            :format-tooltip="formatTooltip"
            :disabled="!currentShot || currentShot.type !== 'video'"
            @input="onVideoVolumeChange"
            class="volume-slider"
          />
          <span class="volume-value" :class="{ 'disabled': !currentShot || currentShot.type !== 'video' }">{{ videoVolume }}%</span>
        </div>
      </div>

      <!-- 当前分镜音频音量 -->
      <div class="volume-section">
        <div class="volume-label" :class="{ 'disabled': !currentShot || !currentShot.audios || audioVolumeDisabled }">
          <el-icon><Microphone /></el-icon>
          <span>声音</span>
        </div>
        <div class="volume-control">
          <el-slider
            v-model="overallAudioVolume"
            :min="0"
            :max="150"
            :step="1"
            :show-tooltip="true"
            :format-tooltip="formatTooltip"
            :disabled="!currentShot || !currentShot.audios || audioVolumeDisabled"
            @input="onOverallAudioVolumeChange"
            class="volume-slider"
          />
          <span class="volume-value" :class="{ 'disabled': !currentShot || !currentShot.audios || audioVolumeDisabled }">{{ overallAudioVolume }}%</span>
        </div>
      </div>

      <!-- 当前分镜音效音量 -->
      <div class="volume-section">
        <div class="volume-label" :class="{ 'disabled': !currentShot || !currentShot.audios || effectVolumeDisabled }">
          <el-icon><Microphone /></el-icon>
          <span>音效</span>
        </div>
        <div class="volume-control">
          <el-slider
            v-model="overallEffectVolume"
            :min="0"
            :max="150"
            :step="1"
            :show-tooltip="true"
            :format-tooltip="formatTooltip"
            :disabled="!currentShot || !currentShot.audios || effectVolumeDisabled"
            @input="onOverallEffectVolumeChange"
            class="volume-slider"
          />
          <span class="volume-value" :class="{ 'disabled': !currentShot || !currentShot.audios || effectVolumeDisabled }">{{ overallEffectVolume }}%</span>
        </div>
      </div>
      

      <!-- 背景音乐音量 -->
      <div class="volume-section">
        <div class="volume-label" :class="{ 'disabled': !backgroundMusic }">
          <el-icon><Headset /></el-icon>
          <span>背景</span>
        </div>
        <div class="volume-control">
          <el-slider
            v-model="backgroundMusicVolume"
            :min="0"
            :max="150"
            :step="1"
            :show-tooltip="true"
            :format-tooltip="formatTooltip"
            :disabled="!backgroundMusic"
            @input="onBackgroundMusicVolumeChange"
            class="volume-slider"
          />
          <span class="volume-value" :class="{ 'disabled': !backgroundMusic }">{{ backgroundMusicVolume }}%</span>
        </div>
      </div>


    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import {
  Headset,
  VideoCamera,
  Microphone
} from '@element-plus/icons-vue';

// 组件属性
const props = defineProps({
  currentShot: {
    type: Object,
    default: () => null
  },
  backgroundMusic: {
    type: Object,
    default: () => null
  }
});

// 事件
const emit = defineEmits(['update:backgroundMusicVolume', 'update:videoVolume', 'update:audioVolume', 'update:effectVolume']);

// 背景音乐音量 (0-100)
const backgroundMusicVolume = ref(100);

// 视频音量 (0-100)
const videoVolume = ref(100);

// 整体音频音量 (0-100)
const overallAudioVolume = ref(100);
const audioVolumeDisabled = ref(false);

// 整体音效音量 (0-100)
const overallEffectVolume = ref(100);
const effectVolumeDisabled = ref(false);

// 格式化提示文本
const formatTooltip = (value) => {
  return `${value}%`;
};

// 背景音乐音量变化
const onBackgroundMusicVolumeChange = (value) => {
  const normalizedValue = value / 100; // 转换为 0.00-1.00
  emit('update:backgroundMusicVolume', normalizedValue);
};

// 视频音量变化
const onVideoVolumeChange = (value) => {
  const normalizedValue = value / 100; // 转换为 0.00-1.00
  emit('update:videoVolume', normalizedValue);
};

// 整体音频音效音量变化
const onOverallEffectVolumeChange = (value) => {
  const normalizedValue = value / 100; // 转换为 0.00-1.00
  emit('update:effectVolume', normalizedValue);
};

// 整体音频音量变化
const onOverallAudioVolumeChange = (value) => {
  const normalizedValue = value / 100; // 转换为 0.00-1.00
  emit('update:audioVolume', normalizedValue);
};

// 监听背景音乐变化
watch(() => props.backgroundMusic, (newBgMusic) => {
  if (newBgMusic && typeof newBgMusic.volume === 'number') {
    backgroundMusicVolume.value = Math.round(newBgMusic.volume * 100);
  } else if (newBgMusic) {
    // 有背景音乐但没有音量属性，默认100%
    backgroundMusicVolume.value = 100;
  } else {
    // 没有背景音乐，显示0%
    backgroundMusicVolume.value = 0;
  }
}, { immediate: true, deep: true });

// 监听当前分镜变化
watch(() => props.currentShot, (newShot) => {
  if (newShot) {
    // 更新视频音量
    if (newShot.type === 'video' && typeof newShot.videoVolume === 'number') {
      videoVolume.value = Math.round(newShot.videoVolume * 100);
    } else {
      // 如果不是视频分镜，显示为0
      videoVolume.value = newShot.type === 'video' ? 100 : 0;
    }

    // 更新音效音量
    // if (newShot.type === 'effect' && typeof newShot.effectVolume === 'number') {
    //   effectVolume.value = Math.round(newShot.effectVolume * 100);
    // } else {
    //   // 如果不是视频分镜，显示为0
    //   effectVolume.value = newShot.type === 'effect' ? 100 : 0;
    // }

    // 更新整体音频音量（取第一个音频的音量作为整体音量）
    if (newShot.audios && newShot.audios.length > 0) {

      const firstAudio = newShot.audios.find(audio => audio.audioType != 2);
      if (firstAudio && typeof firstAudio.volume === 'number') {
        overallAudioVolume.value = Math.round(firstAudio.volume * 100);
        audioVolumeDisabled.value = false;
      } else {
        overallAudioVolume.value = 0;
        audioVolumeDisabled.value = true;
      }

      const firstEffectAudio = newShot.audios.find(audio => audio.audioType == 2);
      if (firstEffectAudio && typeof firstEffectAudio.volume === 'number') {
        overallEffectVolume.value = Math.round(firstEffectAudio.volume * 100);
        effectVolumeDisabled.value = false;
      } else {
        overallEffectVolume.value = 0;
        effectVolumeDisabled.value = true;
      }
      
    } else {
      // 如果没有音频，显示为0
      overallAudioVolume.value = 0;
      overallEffectVolume.value = 0;
      effectVolumeDisabled.value = true;
      audioVolumeDisabled.value = true;
    }
  } else {
    // 如果没有选中分镜，显示为0
    videoVolume.value = 0;
      overallAudioVolume.value = 0;
      overallEffectVolume.value = 0;
      effectVolumeDisabled.value = true;
      audioVolumeDisabled.value = true;
  }
}, { immediate: true, deep: true });

// 组件挂载时初始化
onMounted(() => {
  // 初始化背景音乐音量
  if (props.backgroundMusic && typeof props.backgroundMusic.volume === 'number') {
    backgroundMusicVolume.value = Math.round(props.backgroundMusic.volume * 100);
  } else if (props.backgroundMusic) {
    // 有背景音乐但没有音量属性，默认100%
    backgroundMusicVolume.value = 100;
  } else {
    // 没有背景音乐，显示0%
    backgroundMusicVolume.value = 0;
  }

  // 初始化当前分镜音量
  if (props.currentShot) {
    // 初始化视频音量
    if (props.currentShot.type === 'video' && typeof props.currentShot.videoVolume === 'number') {
      videoVolume.value = Math.round(props.currentShot.videoVolume * 100);
    } else {
      videoVolume.value = props.currentShot.type === 'video' ? 100 : 0;
    }

    // 初始化整体音频音量
    if (props.currentShot.audios && props.currentShot.audios.length > 0) {
      const firstAudio = props.currentShot.audios[0];
      if (typeof firstAudio.volume === 'number') {
        overallAudioVolume.value = Math.round(firstAudio.volume * 100);
      } else {
        overallAudioVolume.value = 100;
      }
    } else {
      overallAudioVolume.value = 0;
    }
  } else {
    // 没有分镜时显示为0
    videoVolume.value = 0;
    overallAudioVolume.value = 0;
  }
});
</script>

<style scoped>
.volume-control-panel {
  background: #ffffff;
  border-radius: 8px;
  border: none;
  overflow: hidden;
}

body.dark .volume-control-panel {
  background: var(--bg-secondary-video);
}

.panel-header {
  padding: 8px 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #f8f9fa;
}

body.dark .panel-header {
  background: var(--bg-tertiary-video);
  border-bottom-color: var(--border-color-dark);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

body.dark .header-title {
  color: var(--text-primary-dark);
}

.panel-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.volume-section {
  margin-bottom: 4px;
  display: flex;
  flex-direction: row;
  gap: 8px;
}

.volume-section:last-child {
  margin-bottom: 0;
}

.volume-label {
  display: flex;
  align-items: center;
  gap: 6px;
  /* margin-bottom: 8px; */
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

body.dark .volume-label {
  color: var(--text-secondary-dark);
}

.volume-label.disabled {
  opacity: 0.5;
  color: #c0c4cc;
}

body.dark .volume-label.disabled {
  color: #606266;
}

.volume-control {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
}

.volume-slider {
  flex: 1;
}

.volume-value {
  font-size: 12px;
  color: #909399;
  min-width: 35px;
  text-align: right;
}

body.dark .volume-value {
  color: var(--text-tertiary-dark);
}

.volume-value.disabled {
  opacity: 0.5;
  color: #c0c4cc;
}

body.dark .volume-value.disabled {
  color: #606266;
}





/* Element Plus 滑块样式覆盖 */
:deep(.el-slider__runway) {
  background-color: #e4e7ed;
}

body.dark :deep(.el-slider__runway) {
  background-color: var(--border-color-dark);
}

:deep(.el-slider__bar) {
  background-color: #409eff;
}

:deep(.el-slider__button) {
  border-color: #409eff;
}
</style>
