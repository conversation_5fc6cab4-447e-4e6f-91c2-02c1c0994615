<template>
  <div class="playback-controls">
    <div class="controls-container">
      <div class="shot-navigation-info">
        <!-- 字幕开关 -->
        <div class="subtitle-switch">
          <el-switch v-model="subtitlesEnabled" inline-prompt active-text="字幕" inactive-text="字幕" size="large"
            class="subtitle-switch-component" style="--el-switch-on-color: #706cefd7" />
          <div class="add-background-music" @click="addBackgroundMusic">添加背景声</div>

          <!-- 音量设置下拉面板 -->
          <DropdownPanel v-model="isVolumeControlVisible" position="bottom" align="center" :min-width="280" dropdown-id="volume-control-panel">
            <template #trigger>
              <div class="add-background-music">分镜音量</div>
            </template>
            <VolumeControlPanel
              :current-shot="currentShot"
              :background-music="backgroundMusic"
              @update:backgroundMusicVolume="handleBackgroundMusicVolumeChange"
              @update:videoVolume="handleVideoVolumeChange"
              @update:audioVolume="handleAudioVolumeChange"
              @update:effectVolume="handleEffectVolumeChange"
            />
          </DropdownPanel>
        </div>
      </div>
      <!-- 播放控制 -->
      <div class="play-control">
        <div class="control-button play-pause" @click="togglePlayPause">
          <el-icon v-if="isPlaying">
            <VideoPause />
          </el-icon>
          <el-icon v-else>
            <VideoPlay />
          </el-icon>
        </div>
        <!-- 播放进度 -->
        <div class="playback-time">
          <div class="playback-time-current">{{ formattedCurrentTime }}</div>
          <span>/{{ formattedTotalDuration }}</span>
        </div>

        <div class="control-button fullscreen" @click="toggleFullScreen" v-if="isPlaying">
          <el-icon>
            <FullScreen />
          </el-icon>
        </div>
      </div>

      <!-- 视图模式选择器 -->
      <div class="view-mode-container">
        <!-- 时间轴缩放大小 -->
        <div class="timeline-scale-container" v-if="shotViewMode === 'timeline'">
          <div class="scale-slider-container">
            <el-icon class="zoom-icon" @click="decreaseTimelineScale"><ZoomOut /></el-icon>
            <el-slider 
              v-model="localTimelineScale" 
              :min="20" 
              :max="80" 
              :step="2"
              :show-tooltip="false"
              class="timeline-slider" 
              @change="handleTimelineScaleChange" />
            <el-icon class="zoom-icon" @click="increaseTimelineScale"><ZoomIn /></el-icon>
          </div>
        </div>

        <DropdownPanel v-model="isViewModePanelVisible" position="bottom" align="center" :min-width="100" dropdown-id="view-mode-selector">
          <template #trigger>
            <div class="menu-button">
              <el-icon v-if="shotViewMode === 'default'">
                <Grid />
              </el-icon>
              <el-icon v-else>
                <Menu />
              </el-icon>
              <span>{{ shotViewMode === 'default' ? '默认视图' : '时间线视图' }}</span>
            </div>
          </template>

          <!-- 视图模式选项列表 -->
          <div class="view-mode-options">
            <div v-for="option in viewModeOptions" :key="option.value" class="animation-option-item"
              :class="{ 'active': shotViewMode === option.value }" @click="selectViewMode(option.value)">
              <div class="option-item-text">{{ option.label }}</div>
            </div>
          </div>
        </DropdownPanel>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { VideoPause, VideoPlay, ZoomIn, ZoomOut, Grid, Menu, FullScreen } from '@element-plus/icons-vue';
import DropdownPanel from '@/components/parent/DropdownPanel.vue';
import VolumeControlPanel from '@/components/videoEdit/VolumeControlPanel.vue';
import { ElMessageBox } from 'element-plus';

// 定义组件的 props
const props = defineProps({
  isPlaying: {
    type: Boolean,
    default: false
  },
  currentPlaybackTime: {
    type: Number,
    default: 0
  },
  totalPlaybackDuration: {
    type: Number,
    default: 0
  },
  showSubtitles: {
    type: Boolean,
    default: true
  },
  shotViewMode: {
    type: String,
    default: 'default'
  },
  timelineScale: {
    type: Number,
    default: 24
  },
  currentShot: {
    type: Object,
    default: () => null
  },
  backgroundMusic: {
    type: Object,
    default: () => null
  }
});

// 定义组件的事件
const emit = defineEmits([
  'play',
  'pause',
  'update:showSubtitles',
  'update:shotViewMode',
  'update:timelineScale',
  'toggle-fullscreen',
  'update:backgroundMusicVolume',
  'update:videoVolume',
  'update:audioVolume',
  'update:effectVolume'
]);

// 本地状态
const isViewModePanelVisible = ref(false);
const isVolumeControlVisible = ref(false);
const localTimelineScale = ref(props.timelineScale);
const subtitlesEnabled = ref(props.showSubtitles);

// 视图模式选项
const viewModeOptions = [
  { label: '默认视图', value: 'default', icon: 'Grid' },
  { label: '时间线视图', value: 'timeline', icon: 'Menu' },
];

// 监听 props 变化，更新本地状态
watch(() => props.timelineScale, (newValue) => {
  localTimelineScale.value = newValue;
});

watch(() => props.showSubtitles, (newValue) => {
  subtitlesEnabled.value = newValue;
});

// 计算属性：格式化时间
const formattedCurrentTime = computed(() => {
  return formatTime(props.currentPlaybackTime);
});

const formattedTotalDuration = computed(() => {
  return formatTime(props.totalPlaybackDuration);
});

// 格式化时间，将秒转换为 hh:mm:ss 格式
const formatTime = (seconds) => {
  if (!seconds) return '00:00:00';

  const hours = Math.floor(seconds / 3600);
  seconds %= 3600;
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);

  return `${String(hours).padStart(2, '0')}:${String(mins).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
};

// 切换播放/暂停
const togglePlayPause = () => {
  if (props.isPlaying) {
    emit('pause');
  } else {
    emit('play');
  }
};

// 选择视图模式
const selectViewMode = (mode) => {
  emit('update:shotViewMode', mode);
  isViewModePanelVisible.value = false;
};

// 监听字幕开关变化
watch(subtitlesEnabled, (newValue) => {
  emit('update:showSubtitles', newValue);
});

// 处理时间轴缩放比例变化
const handleTimelineScaleChange = (value) => {
  emit('update:timelineScale', value);
  // 保存到本地存储，以便下次打开时恢复
  localStorage.setItem('timelineScale', value.toString());
};

// 增加时间轴缩放级别
const increaseTimelineScale = () => {
  // 确保不超过最大值
  const newValue = Math.min(localTimelineScale.value + 2, 80);
  if (newValue !== localTimelineScale.value) {
    localTimelineScale.value = newValue;
    handleTimelineScaleChange(newValue);
  }
};

// 减少时间轴缩放级别
const decreaseTimelineScale = () => {
  // 确保不低于最小值
  const newValue = Math.max(localTimelineScale.value - 2, 20);
  if (newValue !== localTimelineScale.value) {
    localTimelineScale.value = newValue;
    handleTimelineScaleChange(newValue);
  }
};

const addBackgroundMusic = () => {
  // console.log('addBackgroundMusic');
  ElMessageBox.alert('请在右侧素材库中添加或选择已有背景音乐应用', '完善中', {
    confirmButtonText: '知道了',
  }).then(() => {

  });
}

// 切换全屏
const toggleFullScreen = () => {
  emit('toggle-fullscreen');
};

// 音量控制事件处理函数
const handleBackgroundMusicVolumeChange = (volume) => {
  emit('update:backgroundMusicVolume', volume);
};

const handleVideoVolumeChange = (volume) => {
  emit('update:videoVolume', volume);
};

const handleAudioVolumeChange = (volume) => {
  emit('update:audioVolume', volume);
};

const handleEffectVolumeChange = (volume) => {
  emit('update:effectVolume', volume);
};

</script>

<style scoped>
/* 播放控制区域 */
.playback-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 0 0 6px 6px;
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.2);
  background-color: #ffffff;
  margin-bottom: 6px;
}

.add-background-music{
  font-size: 12px;
  color: #409eff;
  cursor: pointer;
  margin-left: 10px;
  border: 1px solid #409eff;
  border-radius: 8px;
  padding: 3px 8px;
  background-color: transparent;
  color: #409eff;
  transition: all 0.3s;
}

body.dark .playback-controls {
  box-shadow: 0 0 1px 0 rgba(123, 123, 123, 0.322);
  background-color: var(--bg-secondary-video);
}

.controls-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: row;
  padding: 0;
}

.shot-navigation-info {
  flex: 1;
}

/* 视图模式选择器 */
.view-mode-container {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  border-radius: 18px;
  background-color: #ffffff;
  padding: 4px;
}

/* 时间轴缩放容器 */
.timeline-scale-container {
  /* margin-bottom: 8px; */
}

.scale-slider-container {
  display: flex;
  align-items: center;
  /* padding: 0 8px; */
  gap: 8px;
}

.timeline-slider {
  width: 100px;
}

.zoom-icon {
  font-size: 20px;
  color: #606266;
  cursor: pointer;
}

body.dark .zoom-icon {
  color: var(--text-secondary);
}

body.dark .view-mode-container {
  background-color: var(--bg-secondary-video);
  border-color: var(--border-color);
}

.menu-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.menu-button:hover {
  background-color: #ecf5ff;
  color: #409eff;
}

body.dark .menu-button {
  color: var(--text-secondary);
}

body.dark .menu-button:hover {
  background-color: var(--bg-quaternary);
  color: var(--primary-color);
}

.view-mode-options {
  padding: 10px;
  min-width: 100px;
}

.animation-option-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
}

.option-item-text {
  text-align: center;
  width: 100%;
}

.animation-option-item:hover {
  background-color: #f5f7fa;
}

.animation-option-item.active {
  background-color: #ecf5ff;
  color: #409eff;
}

body.dark .animation-option-item:hover {
  background-color: var(--bg-tertiary);
}

body.dark .animation-option-item.active {
  background-color: var(--bg-quaternary);
  color: var(--primary-color);
}

/* 播放控制 */
.play-control {
  /* flex: 1; */
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-button .el-icon {
  font-size: 28px;
  color: #878787;
}

.control-button {
  width: 28px;
  height: 28px;
  border: 1px solid #e4e7ed;
  border-radius: 50%;
  cursor: pointer;
  padding: 0;
  margin: 0 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.control-button:hover {
  color: var(--primary-color);
}

.control-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-button.play-pause {
  width: 32px;
  height: 32px;
  color: var(--primary-color);
  border: none;
}

.control-button.fullscreen {
  width: 24px;
  height: 24px;
  color: var(--primary-color);
  border: none;
  font-size: 20px;
}

.control-button.play-pause:hover {
  transform: scale(1.1);
}

body.dark .control-button {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-secondary);
}

body.dark .control-button:hover {
  background-color: var(--bg-tertiary);
  color: var(--primary-color);
}

body.dark .control-button.play-pause {
  color: white;
}

body.dark .control-button.play-pause:hover {
  color: var(--primary-color);
}

/* 播放进度 */
.playback-time {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #606266;
}

body.dark .playback-time {
  color: var(--text-secondary);
}

.playback-time-current {
  color: var(--text-secondary);
  font-weight: bold;
  font-size: 14px;
}

body.dark .playback-time-current {
  color: var(--text-secondary);
}

/* 字幕开关 */
.subtitle-switch {
  display: flex;
  align-items: center;
  padding: 0 10px;
}

.subtitle-switch-component {
  margin-right: 5px;
}

/* 自定义字幕开关颜色 */
:deep(.subtitle-switch-component.el-switch.is-checked .el-switch__core) {
  background-color: #706cefd7 !important;
  border-color: #706cefd7 !important;
}

:deep(.subtitle-switch-component.el-switch .el-switch__core:hover) {
  border-color: #706cefd7 !important;
}

body.dark .subtitle-switch {
  color: var(--text-secondary);
}
</style> 